# Gemini CLI 项目概述

## 项目简介

Gemini CLI 是由 Google 开发的命令行AI工作流工具，旨在将 Gemini 模型的强大能力直接带到开发者的终端环境中。该工具提供了一个交互式的 REPL（Read-Eval-Print Loop）环境，让用户能够通过自然语言与 AI 模型交互，并利用丰富的工具集来完成各种开发和运维任务。

## 项目目标

### 核心目标
- **提升开发效率**：通过AI辅助自动化常见的开发任务
- **简化工作流程**：将复杂的操作转化为自然语言交互
- **增强代码理解**：利用大语言模型分析和理解大型代码库
- **安全可控**：在沙箱环境中安全执行AI生成的操作

### 设计理念
- **模块化架构**：清晰的前后端分离，便于扩展和维护
- **工具驱动**：通过可扩展的工具系统增强AI能力
- **用户体验优先**：直观的命令行界面和丰富的主题支持
- **安全第一**：多层安全机制确保系统安全

## 主要功能特性

### 1. 智能代码分析
- **大型代码库理解**：支持超过1M token的上下文窗口
- **多文件分析**：同时分析多个文件和目录
- **Git集成**：智能识别和过滤版本控制文件
- **语法高亮**：支持多种编程语言的语法高亮显示

### 2. 多模态输入支持
- **文本处理**：支持纯文本输入和处理
- **图像分析**：支持PNG、JPG、GIF、WEBP、SVG、BMP格式
- **PDF文档**：直接读取和分析PDF文件内容
- **文件系统操作**：读取、写入、搜索本地文件

### 3. 强大的工具生态
- **文件系统工具**：读写文件、目录列表、文件搜索
- **Shell集成**：安全执行系统命令
- **Web工具**：网页抓取和搜索功能
- **内存管理**：跨会话的信息存储和检索
- **MCP服务器**：支持Model Context Protocol扩展

### 4. 灵活的配置系统
- **多层配置**：用户级、项目级、环境变量配置
- **认证方式**：支持API Key、OAuth2、Vertex AI多种认证
- **主题定制**：丰富的UI主题选择
- **沙箱配置**：Docker/Podman容器化执行环境

### 5. 开发者友好
- **非交互模式**：支持脚本化和自动化使用
- **扩展机制**：插件和扩展开发支持
- **调试功能**：详细的调试和日志功能
- **遥测系统**：使用情况统计和错误报告

## 技术栈概览

### 核心技术
- **运行时**：Node.js 18+ 
- **编程语言**：TypeScript
- **模块系统**：ES Modules
- **包管理**：npm workspaces

### 前端技术栈
- **UI框架**：React 18
- **终端UI**：Ink 5.x
- **主题系统**：自定义主题引擎
- **语法高亮**：highlight.js + lowlight

### 后端技术栈
- **API客户端**：@google/genai
- **认证**：google-auth-library + OAuth2
- **文件处理**：Node.js fs + glob + micromatch
- **网络请求**：undici
- **容器化**：Docker/Podman支持

### 构建和开发工具
- **构建工具**：esbuild
- **测试框架**：Vitest
- **代码检查**：ESLint + TypeScript
- **代码格式化**：Prettier
- **CI/CD**：GitHub Actions

### 外部集成
- **AI模型**：Google Gemini API
- **搜索服务**：Google Search API
- **遥测服务**：OpenTelemetry
- **容器运行时**：Docker/Podman

## 架构概览

### 整体架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   CLI Package   │    │  Core Package   │    │  Gemini API     │
│   (Frontend)    │◄──►│   (Backend)     │◄──►│   (External)    │
│                 │    │                 │    │                 │
│ • UI Components │    │ • API Client    │    │ • LLM Models    │
│ • User Input    │    │ • Tool Registry │    │ • Embeddings    │
│ • Display       │    │ • Prompt Mgmt   │    │ • Function Call │
│ • Configuration │    │ • Session Mgmt  │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │
         │                       │
         ▼                       ▼
┌─────────────────┐    ┌─────────────────┐
│   Tool System   │    │  Sandbox Env    │
│                 │    │                 │
│ • File System   │    │ • Docker/Podman │
│ • Shell Exec    │    │ • Isolation     │
│ • Web Fetch     │    │ • Security      │
│ • Memory Store  │    │ • Resource Mgmt │
└─────────────────┘    └─────────────────┘
```

### 核心组件
1. **CLI Package** (`packages/cli`)
   - 用户界面和交互逻辑
   - 配置管理和主题系统
   - 非交互模式支持

2. **Core Package** (`packages/core`)
   - Gemini API集成
   - 工具注册和执行
   - 会话和状态管理

3. **Tool System**
   - 可扩展的工具框架
   - 内置工具集合
   - MCP服务器支持

4. **Sandbox Environment**
   - 容器化执行环境
   - 安全隔离机制
   - 资源限制和监控

## 项目结构

```
gemini-cli/
├── packages/
│   ├── cli/                 # 前端CLI包
│   │   ├── src/
│   │   │   ├── config/      # 配置管理
│   │   │   ├── ui/          # UI组件
│   │   │   └── utils/       # 工具函数
│   │   └── package.json
│   └── core/                # 后端核心包
│       ├── src/
│       │   ├── core/        # 核心逻辑
│       │   ├── tools/       # 工具实现
│       │   ├── config/      # 配置系统
│       │   ├── services/    # 服务层
│       │   └── utils/       # 工具函数
│       └── package.json
├── docs/                    # 文档目录
├── scripts/                 # 构建脚本
├── integration-tests/       # 集成测试
├── bundle/                  # 打包输出
└── package.json            # 根包配置
```

## 版本信息

- **当前版本**：0.1.4
- **Node.js要求**：>=18
- **许可证**：Apache-2.0
- **仓库地址**：https://github.com/google-gemini/gemini-cli

## 快速开始

### 安装方式
```bash
# 直接运行（推荐）
npx https://github.com/google-gemini/gemini-cli

# 全局安装
npm install -g @google/gemini-cli
gemini
```

### 基本使用
```bash
# 启动交互模式
gemini

# 非交互模式
echo "解释这个项目的架构" | gemini
gemini -p "生成一个React组件"
```

### 认证配置
```bash
# 设置API Key
export GEMINI_API_KEY="your-api-key"

# 或使用OAuth2登录
gemini  # 首次运行会引导认证流程
```

## 下一步

本文档提供了Gemini CLI的整体概览。要深入了解具体技术细节，请参考：

- [架构设计文档](./02-architecture-design.md) - 详细的系统架构和设计模式
- [API和接口文档](./03-api-interfaces.md) - 核心API和扩展接口
- [开发指南](./04-development-guide.md) - 开发环境搭建和贡献指南
- [部署和运维文档](./05-deployment-operations.md) - 部署配置和运维指南
