## 电商网站产品需求文档（PRD）

### 文档信息
- **版本**：v1.0（MVP）
- **负责人**：产品/技术负责人（待指派）
- **状态**：评审中
- **目标上线时间**：T0+6 周（详见里程碑）

## 一、背景与目标
- **业务背景**：搭建标准 B2C 自营电商平台，支持商品展示、下单、支付、履约、评价的全链路能力。
- **核心目标**
  - **GMV**：首月累计 ≥ ¥500,000
  - **转化率**：访问→支付转化 ≥ 2.0%
  - **复购率（30 天）**：≥ 15%
  - **履约时效**：下单后48小时内发货 ≥ 95%

## 二、范围（Scope）
- **In Scope（MVP）**
  - 商品与类目、搜索与筛选、商品详情、购物车、收银台（地址/发票/优惠）、第三方支付（微信/支付宝/Stripe 其一）、订单与物流跟踪、评价、账户中心、消息通知（邮件/站内）、基础营销（优惠券/满减）、后台管理（商品/库存/订单/营销/用户/权限）
- **Out of Scope（后续版本）**
  - 多商家入驻、跨境税费、虚拟商品、直播电商、商家结算分账、社交裂变、私域积分体系

## 三、用户与角色
- **访客**：浏览、搜索、收藏、加入购物车
- **注册用户**：下单支付、收货地址管理、售后、评价
- **运营**：投放活动、内容管理、SEO
- **客服**：订单查询、售后/退款处理
- **管理员**：配置、风控、权限
- **权限矩阵（简）**：访客 < 注册用户 < 客服 < 运营 < 管理员（均为最小权限原则）

## 四、用户旅程（简）
1) 触达（SEO/广告/直达）→ 2) 浏览列表/搜索 → 3) 商品详情 → 4) 加购 → 5) 结算（地址/发票/优惠）→ 6) 支付 → 7) 发货/收货 → 8) 评价/售后 → 9) 复购

## 五、业务流程（下单全链路）
选品 → 加购 → 结算校验（库存/价格/优惠）→ 创建订单（待支付）→ 唤起支付 → 支付回调（成功/失败）→ 减库存 → 生成出库单 → 物流发货 → 收货/超时签收 → 评价 → 订单完结（售后通道贯穿）

## 六、功能需求
### 6.1 商品与类目
- 类目管理：最多三级；排序、上下架、SEO 字段（title/keywords/description）
- 商品管理：SPU/SKU、主图/视频、规格、价格、库存、标签、上/下架、富文本详情
- 库存策略：下单占库存（15分钟未支付释放）或支付减库存（二选一，可配置）

### 6.2 搜索与推荐
- 关键字搜索、类目/价格区间/品牌/属性筛选，排序（综合/销量/上架时间/价格），搜索联想
- 推荐：同类目热销、详情页“看了又看/买了还买”

### 6.3 商品详情页
- 图文详情、规格选择、库存/价格联动、配送范围/时效、评价概览与筛选、加购/立即购买

### 6.4 购物车
- 加减数量、勾选结算、无货/下架标识、凑单推荐、选中总价/总数、跨端持久化（登录后）

### 6.5 结算/收银台
- 地址（新增/编辑/默认）、发票（普通/专票，后续版本）、配送方式、优惠（优惠券/满减/包邮）
- 实时价格校验（活动价、阶梯价、运费模板、税费后续）
- 下单防重、并发校验、风控（黑名单/高风险限购）

### 6.6 支付
- 微信/支付宝/Stripe（MVP 至少一种），支付回调幂等处理，失败重试/补单
- 订单状态：待支付→已支付→待发货→已发货→已收货→已完成；异常：已取消/退款中/退款成功/关闭

### 6.7 订单与物流
- 订单列表/详情、取消/售后申请、物流节点查询、签收确认、电子面单对接（后续）

### 6.8 评价与内容
- 仅已收货用户可评，文字/图片，追评，敏感词与图片审核（后续）
- 前台展示好评度、可按“有图/追评/最新/有用”筛选

### 6.9 优惠与营销（MVP）
- 优惠券：满减/直减、发放渠道（注册礼、活动页、手动发放）、使用规则（类目/商品/时间）
- 满减：按商品行或按订单级，阶梯档，叠加规则
- 活动页：简单版聚合落地页

### 6.10 账户中心
- 资料与安全（密码/二次验证后续）、收货地址、订单、优惠券、消息、售后单

### 6.11 消息通知
- 邮件/站内信（MVP），短信（后续）；触发：下单、支付、发货、退款、优惠到期提醒

### 6.12 SEO 与内容
- 静态化路由、可配置 meta/OG、站点地图、结构化数据（Breadcrumb/产品）

### 6.13 管理后台（MVP）
- 商品（SPU&SKU）、类目、库存、价格
- 订单（查询/导出/发货/备注）、售后（同意/拒绝/退款）
- 营销（优惠券/满减）、用户（黑名单/标签）
- 内容（轮播/楼层/专题）
- 权限（RBAC 角色/菜单/数据范围）

## 七、非功能需求
- 性能：FCP ≤ 2.0s（P75），页面资源 < 600KB（首屏），接口 P95 ≤ 300ms
- 高可用：核心链路 99.9%；支付回调/订单创建幂等；重试/补偿机制
- 安全与合规：OWASP Top 10、敏感信息加密（传输/存储）、最小权限、审计日志、GDPR/隐私条款
- 可观测性：日志/指标/链路追踪；关键告警（下单/支付错误率）
- 可扩展性：水平扩展、读写分离、缓存（商品/价格/库存）、限流
- 易用性/无障碍：键盘可达、ARIA、对比度达标
- 多语言/多币种（后续可扩展）

## 八、数据与报表
- 核心指标：UV、加购率、支付转化、客单价、退款率、履约时效、活动ROI
- 漏斗：PV→商品曝光→详情→加购→结算→支付成功
- 用户：新增/活跃/留存、复购、LTV、分层（RFM）

## 九、数据模型（简要）
- User(id, email, mobile, password_hash, status, created_at)
- Category(id, name, parent_id, sort, seo)
- Product(SPU)(id, title, subtitle, category_id, status, created_at)
- SKU(id, spu_id, attrs_json, price, stock, barcode)
- CartItem(user_id, sku_id, quantity, selected)
- Order(id, user_id, status, total_amount, pay_amount, pay_channel, address_snapshot, invoice_snapshot, created_at)
- OrderItem(order_id, sku_id, price, quantity)
- Coupon(id, type, rule_json, valid_time, status)
- UserCoupon(user_id, coupon_id, status, used_order_id)
- Shipment(order_id, express_code, tracking_no, shipped_at, delivered_at)
- Review(order_id, sku_id, rating, content, images, created_at)

## 十、API 概要（REST，MVP）
- 商品：GET `/api/products`、GET `/api/products/{id}`
- 购物车：GET/POST/PUT/DELETE `/api/cart/items`
- 结算：POST `/api/checkout/verify`
- 订单：POST `/api/orders`、GET `/api/orders`、GET `/api/orders/{id}`、POST `/api/orders/{id}/cancel`
- 支付：POST `/api/pay/{orderId}`、POST `/api/pay/notify`（回调）
- 物流：GET `/api/orders/{id}/shipment`
- 优惠：GET `/api/coupons`、POST `/api/coupons/{id}/claim`
- 用户：POST `/api/auth/login|register|logout`、GET `/api/me`
- 后台：以 `/admin/*` 命名空间，需 RBAC 鉴权与审计

## 十一、权限与风控
- 鉴权：JWT/OAuth2，短期令牌+刷新令牌
- RBAC：用户-角色-权限-资源
- 风控：限购（人/设备/收件信息）、高风险支付拦截、IP 频控、敏感操作二次校验

## 十二、环境与部署
- 环境：Dev/Staging/Prod；灰度发布与回滚
- 依赖：对象存储（图片）、CDN、支付网关、邮件服务、日志/监控平台
- 备份：数据库每日全量+增量，关键配置版本化

## 十三、验收标准（示例）
- 下单：当用户选择有库存的 SKU 且地址有效时，点击“提交订单”，应创建待支付订单并返回支付页链接，订单状态=待支付
- 支付回调：支付网关回调成功后，订单状态=已支付，库存扣减一次，幂等校验通过
- 发货：管理员录入物流单号后，用户可在订单详情看到物流轨迹
- 优惠：当满足满减阶梯规则时，订单实付金额正确减少且记录优惠明细

## 十四、里程碑与优先级
- MVP（T0+6 周）：商品/类目、详情、购物车、结算、单一支付、订单、物流查询、评价、优惠券、基础后台、SEO 基础
- MMP（T0+12 周）：多支付通道、发票、售后全流程、活动玩法扩展、短信、内容化首页、A/B 测试
- V2：多商家、分账、积分会员、直播/导购、国际化

## 十五、依赖与风险
- 支付对接周期与合规要求
- 物流轨迹第三方稳定性
- 大促峰值压测与扩容
- 商品内容与审核合规

## 十六、研发 DoD（完成定义）
- 单测覆盖率 ≥ 70%，关键流程集成测试覆盖
- 监控/告警已配置（下单/支付/库存/回调）
- 回滚预案与演练完成
- 安全扫描通过（依赖/容器/应用）

## 十七、术语
- SPU/SKU、GMV、转化率、客单价、履约、MVP/MMP、RBAC、幂等、灰度

> 附注：`ecommerce/` 前端原型可作为 MVP 前台功能演示与验收基线。


