/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import { describe, it, expect, beforeEach } from 'vitest';
import { LruCache } from './LruCache.js';

describe('LruCache', () => {
  let cache: LruCache<string, number>;

  beforeEach(() => {
    cache = new LruCache<string, number>(3);
  });

  describe('constructor', () => {
    it('should create a cache with specified maxSize', () => {
      const newCache = new LruCache<string, string>(5);
      expect(newCache).toBeDefined();
    });

    it('should handle zero maxSize', () => {
      const newCache = new LruCache<string, string>(0);
      expect(newCache).toBeDefined();
    });
  });

  describe('set and get', () => {
    it('should store and retrieve values', () => {
      cache.set('key1', 100);
      expect(cache.get('key1')).toBe(100);
    });

    it('should return undefined for non-existent keys', () => {
      expect(cache.get('nonexistent')).toBeUndefined();
    });

    it('should update existing values', () => {
      cache.set('key1', 100);
      cache.set('key1', 200);
      expect(cache.get('key1')).toBe(200);
    });
  });

  describe('LRU eviction', () => {
    it('should evict least recently used item when cache is full', () => {
      // Fill cache to capacity
      cache.set('key1', 1);
      cache.set('key2', 2);
      cache.set('key3', 3);

      // Add one more item, should evict key1
      cache.set('key4', 4);

      expect(cache.get('key1')).toBeUndefined();
      expect(cache.get('key2')).toBe(2);
      expect(cache.get('key3')).toBe(3);
      expect(cache.get('key4')).toBe(4);
    });

    it('should update access order when getting values', () => {
      cache.set('key1', 1);
      cache.set('key2', 2);
      cache.set('key3', 3);

      // Access key1 to make it recently used
      cache.get('key1');

      // Add key4, should evict key2 (least recently used)
      cache.set('key4', 4);

      expect(cache.get('key1')).toBe(1); // Still exists
      expect(cache.get('key2')).toBeUndefined(); // Evicted
      expect(cache.get('key3')).toBe(3);
      expect(cache.get('key4')).toBe(4);
    });

    it('should handle updating existing key without eviction', () => {
      cache.set('key1', 1);
      cache.set('key2', 2);
      cache.set('key3', 3);

      // Update existing key
      cache.set('key2', 22);

      expect(cache.get('key1')).toBe(1);
      expect(cache.get('key2')).toBe(22);
      expect(cache.get('key3')).toBe(3);
    });
  });

  describe('clear', () => {
    it('should remove all items from cache', () => {
      cache.set('key1', 1);
      cache.set('key2', 2);
      cache.set('key3', 3);

      cache.clear();

      expect(cache.get('key1')).toBeUndefined();
      expect(cache.get('key2')).toBeUndefined();
      expect(cache.get('key3')).toBeUndefined();
    });

    it('should allow adding items after clear', () => {
      cache.set('key1', 1);
      cache.clear();
      cache.set('key2', 2);

      expect(cache.get('key1')).toBeUndefined();
      expect(cache.get('key2')).toBe(2);
    });
  });

  describe('edge cases', () => {
    it('should handle cache with maxSize 1', () => {
      const smallCache = new LruCache<string, number>(1);

      smallCache.set('key1', 1);
      expect(smallCache.get('key1')).toBe(1);

      smallCache.set('key2', 2);
      expect(smallCache.get('key1')).toBeUndefined();
      expect(smallCache.get('key2')).toBe(2);
    });

    it('should handle cache with maxSize 0', () => {
      const zeroCache = new LruCache<string, number>(0);

      zeroCache.set('key1', 1);
      expect(zeroCache.get('key1')).toBeUndefined();
    });

    it('should work with different key and value types', () => {
      const numberCache = new LruCache<number, string>(2);

      numberCache.set(1, 'one');
      numberCache.set(2, 'two');

      expect(numberCache.get(1)).toBe('one');
      expect(numberCache.get(2)).toBe('two');

      const objCache = new LruCache<string, { value: number }>(2);
      const obj1 = { value: 1 };
      const obj2 = { value: 2 };

      objCache.set('obj1', obj1);
      objCache.set('obj2', obj2);

      expect(objCache.get('obj1')).toBe(obj1);
      expect(objCache.get('obj2')).toBe(obj2);
    });
  });
});