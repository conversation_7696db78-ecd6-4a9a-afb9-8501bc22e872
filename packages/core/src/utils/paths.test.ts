/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import { 
  tildeifyPath, 
  shortenPath, 
  makeRelative, 
  escapePath, 
  unescapePath, 
  getProjectHash, 
  getProjectTempDir,
  GEMINI_DIR 
} from './paths.js';
import * as os from 'os';
import * as path from 'path';
import * as crypto from 'crypto';

// Mock os module
vi.mock('os', () => ({
  homedir: vi.fn(),
}));

describe('paths utilities', () => {
  const mockHomedir = '/Users/<USER>';
  
  beforeEach(() => {
    vi.mocked(os.homedir).mockReturnValue(mockHomedir);
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('tildeifyPath', () => {
    it('should replace home directory with tilde', () => {
      const testPath = '/Users/<USER>/Documents/project';
      const result = tildeifyPath(testPath);
      expect(result).toBe('~/Documents/project');
    });

    it('should not replace if path does not start with home directory', () => {
      const testPath = '/usr/local/bin';
      const result = tildeifyPath(testPath);
      expect(result).toBe('/usr/local/bin');
    });

    it('should handle exact home directory path', () => {
      const result = tildeifyPath(mockHomedir);
      expect(result).toBe('~');
    });

    it('should handle empty string', () => {
      const result = tildeifyPath('');
      expect(result).toBe('');
    });
  });

  describe('shortenPath', () => {
    it('should return path unchanged if under maxLen', () => {
      const shortPath = '/short/path.txt';
      const result = shortenPath(shortPath, 50);
      expect(result).toBe(shortPath);
    });

    it('should shorten long paths correctly', () => {
      const longPath = '/very/long/path/with/many/directories/file.txt';
      const result = shortenPath(longPath, 30);
      expect(result.length).toBeLessThanOrEqual(30);
      expect(result).toContain('/very');
      expect(result).toContain('file.txt');
    });

    it('should handle single segment after root', () => {
      const singleSegment = '/verylongfilenamethatexceedsmaxlength.txt';
      const result = shortenPath(singleSegment, 20);
      expect(result.length).toBeLessThanOrEqual(20);
      expect(result).toContain('...');
    });

    it('should use default maxLen of 35', () => {
      const longPath = '/very/long/path/with/many/directories/and/subdirectories/file.txt';
      const result = shortenPath(longPath);
      expect(result.length).toBeLessThanOrEqual(35);
    });

    it('should handle Windows paths', () => {
      const windowsPath = 'C:\\Users\\<USER>\\Documents\\very\\long\\path\\file.txt';
      const result = shortenPath(windowsPath, 30);
      expect(result.length).toBeLessThanOrEqual(30);
    });

    it('should handle very small maxLen', () => {
      const path = '/some/path/file.txt';
      const result = shortenPath(path, 5);
      expect(result.length).toBeLessThanOrEqual(5);
      expect(result).toContain('...');
    });
  });

  describe('makeRelative', () => {
    it('should calculate relative path correctly', () => {
      const targetPath = '/Users/<USER>/project/src/index.ts';
      const rootDirectory = '/Users/<USER>/project';
      const result = makeRelative(targetPath, rootDirectory);
      expect(result).toBe(path.join('src', 'index.ts'));
    });

    it('should return "." for same paths', () => {
      const samePath = '/Users/<USER>/project';
      const result = makeRelative(samePath, samePath);
      expect(result).toBe('.');
    });

    it('should handle relative input paths', () => {
      const targetPath = './src/index.ts';
      const rootDirectory = process.cwd();
      const result = makeRelative(targetPath, rootDirectory);
      expect(result).toBe(path.join('src', 'index.ts'));
    });

    it('should handle paths outside root directory', () => {
      const targetPath = '/Users/<USER>/project';
      const rootDirectory = '/Users/<USER>/project';
      const result = makeRelative(targetPath, rootDirectory);
      expect(result).toContain('..');
    });
  });

  describe('escapePath', () => {
    it('should escape spaces in path', () => {
      const pathWithSpaces = '/path with spaces/file name.txt';
      const result = escapePath(pathWithSpaces);
      expect(result).toBe('/path\\ with\\ spaces/file\\ name.txt');
    });

    it('should not double-escape already escaped spaces', () => {
      const alreadyEscaped = '/path\\ with\\ spaces/file.txt';
      const result = escapePath(alreadyEscaped);
      expect(result).toBe('/path\\ with\\ spaces/file.txt');
    });

    it('should handle path with no spaces', () => {
      const noSpaces = '/path/without/spaces/file.txt';
      const result = escapePath(noSpaces);
      expect(result).toBe(noSpaces);
    });

    it('should handle empty string', () => {
      const result = escapePath('');
      expect(result).toBe('');
    });

    it('should handle space at beginning', () => {
      const result = escapePath(' leading space');
      expect(result).toBe('\\ leading\\ space');
    });
  });

  describe('unescapePath', () => {
    it('should unescape escaped spaces', () => {
      const escapedPath = '/path\\ with\\ spaces/file\\ name.txt';
      const result = unescapePath(escapedPath);
      expect(result).toBe('/path with spaces/file name.txt');
    });

    it('should handle path with no escaped spaces', () => {
      const noEscapedSpaces = '/path/without/escaped/spaces.txt';
      const result = unescapePath(noEscapedSpaces);
      expect(result).toBe(noEscapedSpaces);
    });

    it('should handle empty string', () => {
      const result = unescapePath('');
      expect(result).toBe('');
    });
  });

  describe('getProjectHash', () => {
    it('should generate consistent hash for same path', () => {
      const projectRoot = '/Users/<USER>/project';
      const hash1 = getProjectHash(projectRoot);
      const hash2 = getProjectHash(projectRoot);
      expect(hash1).toBe(hash2);
    });

    it('should generate different hashes for different paths', () => {
      const project1 = '/Users/<USER>/project1';
      const project2 = '/Users/<USER>/project2';
      const hash1 = getProjectHash(project1);
      const hash2 = getProjectHash(project2);
      expect(hash1).not.toBe(hash2);
    });

    it('should generate SHA256 hash', () => {
      const projectRoot = '/Users/<USER>/project';
      const hash = getProjectHash(projectRoot);
      expect(hash).toMatch(/^[a-f0-9]{64}$/);
    });
  });

  describe('getProjectTempDir', () => {
    it('should generate temp dir path with project hash', () => {
      const projectRoot = '/Users/<USER>/project';
      const tempDir = getProjectTempDir(projectRoot);
      const expectedHash = getProjectHash(projectRoot);
      const expectedPath = path.join(mockHomedir, GEMINI_DIR, 'tmp', expectedHash);
      expect(tempDir).toBe(expectedPath);
    });

    it('should generate different temp dirs for different projects', () => {
      const project1 = '/Users/<USER>/project1';
      const project2 = '/Users/<USER>/project2';
      const tempDir1 = getProjectTempDir(project1);
      const tempDir2 = getProjectTempDir(project2);
      expect(tempDir1).not.toBe(tempDir2);
    });
  });

  describe('escapePath and unescapePath round trip', () => {
    it('should preserve original path after escape/unescape cycle', () => {
      const originalPaths = [
        'file with spaces.txt',
        '/path/with spaces/file.txt',
        'file  with  multiple  spaces.txt',
        ' file with spaces ',
        'no-spaces-file.txt',
        '',
      ];

      originalPaths.forEach(originalPath => {
        const escaped = escapePath(originalPath);
        const unescaped = unescapePath(escaped);
        expect(unescaped).toBe(originalPath);
      });
    });
  });

  describe('getProjectHash', () => {
    it('should generate consistent hash for same project root', () => {
      const projectRoot = '/home/<USER>/my-project';
      const hash1 = getProjectHash(projectRoot);
      const hash2 = getProjectHash(projectRoot);
      
      expect(hash1).toBe(hash2);
      expect(hash1).toHaveLength(64); // SHA256 hex length
      expect(hash1).toMatch(/^[a-f0-9]{64}$/);
    });

    it('should generate different hashes for different project roots', () => {
      const root1 = '/home/<USER>/project1';
      const root2 = '/home/<USER>/project2';
      
      const hash1 = getProjectHash(root1);
      const hash2 = getProjectHash(root2);
      
      expect(hash1).not.toBe(hash2);
    });

    it('should generate expected hash value', () => {
      const projectRoot = '/test/project';
      const expectedHash = crypto.createHash('sha256').update(projectRoot).digest('hex');
      
      expect(getProjectHash(projectRoot)).toBe(expectedHash);
    });

    it('should handle special characters in path', () => {
      const projectRoot = '/path/with-special_chars.123/project';
      const hash = getProjectHash(projectRoot);
      
      expect(hash).toHaveLength(64);
      expect(hash).toMatch(/^[a-f0-9]{64}$/);
    });
  });

  describe('getProjectTempDir', () => {
    beforeEach(() => {
      vi.spyOn(os, 'homedir').mockReturnValue('/home/<USER>');
    });

    it('should generate consistent temp dir for same project', () => {
      const projectRoot = '/home/<USER>/my-project';
      const tempDir1 = getProjectTempDir(projectRoot);
      const tempDir2 = getProjectTempDir(projectRoot);
      
      expect(tempDir1).toBe(tempDir2);
    });

    it('should generate different temp dirs for different projects', () => {
      const root1 = '/home/<USER>/project1';
      const root2 = '/home/<USER>/project2';
      
      const tempDir1 = getProjectTempDir(root1);
      const tempDir2 = getProjectTempDir(root2);
      
      expect(tempDir1).not.toBe(tempDir2);
    });

    it('should generate path in home directory with expected structure', () => {
      const projectRoot = '/home/<USER>/my-project';
      const tempDir = getProjectTempDir(projectRoot);
      const hash = getProjectHash(projectRoot);
      
      const expectedPath = path.join('/home/<USER>', GEMINI_DIR, 'tmp', hash);
      expect(tempDir).toBe(expectedPath);
    });

    it('should handle different home directories', () => {
      const projectRoot = '/Users/<USER>/project';
      
      // Test with macOS-style home
      vi.spyOn(os, 'homedir').mockReturnValue('/Users/<USER>');
      const macTempDir = getProjectTempDir(projectRoot);
      
      // Test with Linux-style home
      vi.spyOn(os, 'homedir').mockReturnValue('/home/<USER>');
      const linuxTempDir = getProjectTempDir(projectRoot);
      
      expect(macTempDir).toContain('/Users/<USER>/.gemini/tmp/');
      expect(linuxTempDir).toContain('/home/<USER>/.gemini/tmp/');
      expect(macTempDir).not.toBe(linuxTempDir);
    });

    it('should contain GEMINI_DIR constant in path', () => {
      const projectRoot = '/any/project';
      const tempDir = getProjectTempDir(projectRoot);
      
      expect(tempDir).toContain(GEMINI_DIR);
      expect(tempDir).toContain('tmp');
    });
  });
}); 