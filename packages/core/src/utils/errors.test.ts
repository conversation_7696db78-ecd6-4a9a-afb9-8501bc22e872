/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import { describe, it, expect, vi } from 'vitest';
import { GaxiosError } from 'gaxios';
import {
  isNodeError,
  getErrorMessage,
  ForbiddenError,
  UnauthorizedError,
  BadRequestError,
  toFriendlyError,
} from './errors.js';

describe('errors', () => {
  describe('isNodeError', () => {
    it('should return true for NodeJS.ErrnoException with code property', () => {
      const nodeError = new Error('Test error') as NodeJS.ErrnoException;
      nodeError.code = 'ENOENT';

      expect(isNodeError(nodeError)).toBe(true);
    });

    it('should return false for regular Error without code property', () => {
      const regularError = new Error('Regular error');

      expect(isNodeError(regularError)).toBe(false);
    });

    it('should return false for non-Error objects', () => {
      expect(isNodeError('string error')).toBe(false);
      expect(isNodeError(123)).toBe(false);
      expect(isNodeError(null)).toBe(false);
      expect(isNodeError(undefined)).toBe(false);
    });

    it('should return false for objects with code but not Error instances', () => {
      const fakeError = { code: 'ENOENT', message: 'fake error' };

      expect(isNodeError(fakeError)).toBe(false);
    });
  });

  describe('getErrorMessage', () => {
    it('should return message for Error instances', () => {
      const error = new Error('Test error message');

      expect(getErrorMessage(error)).toBe('Test error message');
    });

    it('should return string representation for non-Error objects', () => {
      expect(getErrorMessage('string error')).toBe('string error');
      expect(getErrorMessage(123)).toBe('123');
      expect(getErrorMessage(null)).toBe('null');
      expect(getErrorMessage(undefined)).toBe('undefined');
    });

    it('should return stringified object for objects', () => {
      const obj = { error: 'test', code: 500 };

      expect(getErrorMessage(obj)).toBe('[object Object]');
    });

    it('should handle errors that throw during stringification', () => {
      const problematicObject = {
        toString: () => {
          throw new Error('toString failed');
        },
      };

      expect(getErrorMessage(problematicObject)).toBe('Failed to get error details');
    });
  });

  describe('Custom Error Classes', () => {
    describe('ForbiddenError', () => {
      it('should create ForbiddenError with message', () => {
        const error = new ForbiddenError('Access denied');

        expect(error).toBeInstanceOf(Error);
        expect(error).toBeInstanceOf(ForbiddenError);
        expect(error.message).toBe('Access denied');
        expect(error.name).toBe('Error');
      });
    });

    describe('UnauthorizedError', () => {
      it('should create UnauthorizedError with message', () => {
        const error = new UnauthorizedError('Authentication required');

        expect(error).toBeInstanceOf(Error);
        expect(error).toBeInstanceOf(UnauthorizedError);
        expect(error.message).toBe('Authentication required');
        expect(error.name).toBe('Error');
      });
    });

    describe('BadRequestError', () => {
      it('should create BadRequestError with message', () => {
        const error = new BadRequestError('Invalid request');

        expect(error).toBeInstanceOf(Error);
        expect(error).toBeInstanceOf(BadRequestError);
        expect(error.message).toBe('Invalid request');
        expect(error.name).toBe('Error');
      });
    });
  });

  describe('toFriendlyError', () => {
    it('should return BadRequestError for GaxiosError with 400 code', () => {
      const gaxiosError = new GaxiosError('Bad request', {}, {
        status: 400,
        statusText: 'Bad Request',
        data: { error: { code: 400, message: 'Invalid parameter' } },
        headers: {},
        config: {},
      });

      const result = toFriendlyError(gaxiosError);

      expect(result).toBeInstanceOf(BadRequestError);
      expect((result as BadRequestError).message).toBe('Invalid parameter');
    });

    it('should return UnauthorizedError for GaxiosError with 401 code', () => {
      const gaxiosError = new GaxiosError('Unauthorized', {}, {
        status: 401,
        statusText: 'Unauthorized',
        data: { error: { code: 401, message: 'Invalid credentials' } },
        headers: {},
        config: {},
      });

      const result = toFriendlyError(gaxiosError);

      expect(result).toBeInstanceOf(UnauthorizedError);
      expect((result as UnauthorizedError).message).toBe('Invalid credentials');
    });

    it('should return ForbiddenError for GaxiosError with 403 code', () => {
      const gaxiosError = new GaxiosError('Forbidden', {}, {
        status: 403,
        statusText: 'Forbidden',
        data: { error: { code: 403, message: 'Access forbidden' } },
        headers: {},
        config: {},
      });

      const result = toFriendlyError(gaxiosError);

      expect(result).toBeInstanceOf(ForbiddenError);
      expect((result as ForbiddenError).message).toBe('Access forbidden');
    });

    it('should return original error for GaxiosError with unknown code', () => {
      const gaxiosError = new GaxiosError('Server error', {}, {
        status: 500,
        statusText: 'Internal Server Error',
        data: { error: { code: 500, message: 'Internal error' } },
        headers: {},
        config: {},
      });

      const result = toFriendlyError(gaxiosError);

      expect(result).toBe(gaxiosError);
    });

    it('should return original error for GaxiosError without error data', () => {
      const gaxiosError = new GaxiosError('Network error', {}, {
        status: 404,
        statusText: 'Not Found',
        data: null,
        headers: {},
        config: {},
      });

      const result = toFriendlyError(gaxiosError);

      expect(result).toBe(gaxiosError);
    });

    it('should return original error for GaxiosError with incomplete error data', () => {
      const gaxiosError = new GaxiosError('Incomplete error', {}, {
        status: 400,
        statusText: 'Bad Request',
        data: { error: { message: 'Missing code' } },
        headers: {},
        config: {},
      });

      const result = toFriendlyError(gaxiosError);

      expect(result).toBe(gaxiosError);
    });

    it('should parse string response data correctly', () => {
      const gaxiosError = new GaxiosError('String response', {}, {
        status: 400,
        statusText: 'Bad Request',
        data: '{"error":{"code":400,"message":"Parsed from string"}}',
        headers: {},
        config: {},
      });

      const result = toFriendlyError(gaxiosError);

      expect(result).toBeInstanceOf(BadRequestError);
      expect((result as BadRequestError).message).toBe('Parsed from string');
    });

    it('should return original error for non-GaxiosError', () => {
      const regularError = new Error('Regular error');

      expect(toFriendlyError(regularError)).toBe(regularError);
      expect(toFriendlyError('string error')).toBe('string error');
      expect(toFriendlyError(null)).toBe(null);
    });
  });
}); 