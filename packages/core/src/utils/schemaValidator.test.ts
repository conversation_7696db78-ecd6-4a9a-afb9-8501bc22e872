/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import { SchemaValidator } from './schemaValidator.js';

describe('SchemaValidator', () => {
  let consoleErrorSpy: ReturnType<typeof vi.spyOn>;

  beforeEach(() => {
    consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
  });

  afterEach(() => {
    consoleErrorSpy.mockRestore();
  });

  describe('validate', () => {
    describe('required fields validation', () => {
      it('should return true when all required fields are present', () => {
        const schema = {
          required: ['name', 'age'],
        };
        const data = {
          name: '<PERSON>',
          age: 30,
        };

        const result = SchemaValidator.validate(schema, data);

        expect(result).toBe(true);
        expect(consoleErrorSpy).not.toHaveBeenCalled();
      });

      it('should return false when required fields are missing', () => {
        const schema = {
          required: ['name', 'age'],
        };
        const data = {
          name: '<PERSON>',
          // age is missing
        };

        const result = SchemaValidator.validate(schema, data);

        expect(result).toBe(false);
        expect(consoleErrorSpy).toHaveBeenCalledWith('Missing required field: age');
      });

      it('should return false when multiple required fields are missing', () => {
        const schema = {
          required: ['name', 'age', 'email'],
        };
        const data = {
          name: 'John',
          // age and email are missing
        };

        const result = SchemaValidator.validate(schema, data);

        expect(result).toBe(false);
        expect(consoleErrorSpy).toHaveBeenCalledWith('Missing required field: age');
        expect(consoleErrorSpy).toHaveBeenCalledWith('Missing required field: email');
      });

      it('should return true when no required fields are specified', () => {
        const schema = {};
        const data = {
          name: 'John',
        };

        const result = SchemaValidator.validate(schema, data);

        expect(result).toBe(true);
        expect(consoleErrorSpy).not.toHaveBeenCalled();
      });

      it('should return true when required array is empty', () => {
        const schema = {
          required: [],
        };
        const data = {
          name: 'John',
        };

        const result = SchemaValidator.validate(schema, data);

        expect(result).toBe(true);
        expect(consoleErrorSpy).not.toHaveBeenCalled();
      });

      it('should handle required fields with undefined values', () => {
        const schema = {
          required: ['name', 'age'],
        };
        const data = {
          name: 'John',
          age: undefined,
        };

        const result = SchemaValidator.validate(schema, data);

        expect(result).toBe(false);
        expect(consoleErrorSpy).toHaveBeenCalledWith('Missing required field: age');
      });

      it('should handle required fields with null values', () => {
        const schema = {
          required: ['name', 'value'],
        };
        const data = {
          name: 'John',
          value: null,
        };

        const result = SchemaValidator.validate(schema, data);

        expect(result).toBe(true); // null is considered present
        expect(consoleErrorSpy).not.toHaveBeenCalled();
      });
    });

    describe('property types validation', () => {
      it('should validate string types correctly', () => {
        const schema = {
          properties: {
            name: { type: 'string' },
            description: { type: 'string' },
          },
        };
        const data = {
          name: 'John',
          description: 'A person',
        };

        const result = SchemaValidator.validate(schema, data);

        expect(result).toBe(true);
        expect(consoleErrorSpy).not.toHaveBeenCalled();
      });

      it('should validate number types correctly', () => {
        const schema = {
          properties: {
            age: { type: 'number' },
            score: { type: 'number' },
          },
        };
        const data = {
          age: 30,
          score: 95.5,
        };

        const result = SchemaValidator.validate(schema, data);

        expect(result).toBe(true);
        expect(consoleErrorSpy).not.toHaveBeenCalled();
      });

      it('should validate boolean types correctly', () => {
        const schema = {
          properties: {
            isActive: { type: 'boolean' },
            verified: { type: 'boolean' },
          },
        };
        const data = {
          isActive: true,
          verified: false,
        };

        const result = SchemaValidator.validate(schema, data);

        expect(result).toBe(true);
        expect(consoleErrorSpy).not.toHaveBeenCalled();
      });

      it('should validate array types correctly', () => {
        const schema = {
          properties: {
            tags: { type: 'array' },
            items: { type: 'array' },
          },
        };
        const data = {
          tags: ['tag1', 'tag2'],
          items: [1, 2, 3],
        };

        const result = SchemaValidator.validate(schema, data);

        expect(result).toBe(true);
        expect(consoleErrorSpy).not.toHaveBeenCalled();
      });

      it('should validate object types correctly', () => {
        const schema = {
          properties: {
            address: { type: 'object' },
            metadata: { type: 'object' },
          },
        };
        const data = {
          address: { street: '123 Main St', city: 'Anytown' },
          metadata: {},
        };

        const result = SchemaValidator.validate(schema, data);

        expect(result).toBe(true);
        expect(consoleErrorSpy).not.toHaveBeenCalled();
      });

      it('should return false for type mismatches', () => {
        const schema = {
          properties: {
            age: { type: 'number' },
            name: { type: 'string' },
            isActive: { type: 'boolean' },
          },
        };
        const data = {
          age: '30', // should be number
          name: 123, // should be string
          isActive: 'true', // should be boolean
        };

        const result = SchemaValidator.validate(schema, data);

        expect(result).toBe(false);
        expect(consoleErrorSpy).toHaveBeenCalledWith(
          'Type mismatch for property "age": expected number, got string'
        );
        expect(consoleErrorSpy).toHaveBeenCalledWith(
          'Type mismatch for property "name": expected string, got number'
        );
        expect(consoleErrorSpy).toHaveBeenCalledWith(
          'Type mismatch for property "isActive": expected boolean, got string'
        );
      });

      it('should handle array type detection correctly', () => {
        const schema = {
          properties: {
            items: { type: 'array' },
            notArray: { type: 'object' },
          },
        };
        const data = {
          items: [1, 2, 3],
          notArray: [1, 2, 3], // array instead of object
        };

        const result = SchemaValidator.validate(schema, data);

        expect(result).toBe(false);
        expect(consoleErrorSpy).toHaveBeenCalledWith(
          'Type mismatch for property "notArray": expected object, got array'
        );
      });

      it('should skip validation for undefined properties', () => {
        const schema = {
          properties: {
            name: { type: 'string' },
            age: { type: 'number' },
          },
        };
        const data = {
          name: 'John',
          // age is undefined/missing
        };

        const result = SchemaValidator.validate(schema, data);

        expect(result).toBe(true);
        expect(consoleErrorSpy).not.toHaveBeenCalled();
      });

      it('should handle properties without type specification', () => {
        const schema = {
          properties: {
            name: { description: 'A name' }, // no type specified
            age: { type: 'number' },
          },
        };
        const data = {
          name: 'John',
          age: 30,
        };

        const result = SchemaValidator.validate(schema, data);

        expect(result).toBe(true);
        expect(consoleErrorSpy).not.toHaveBeenCalled();
      });
    });

    describe('combined validation', () => {
      it('should validate both required fields and types', () => {
        const schema = {
          required: ['name', 'age'],
          properties: {
            name: { type: 'string' },
            age: { type: 'number' },
            email: { type: 'string' },
          },
        };
        const data = {
          name: 'John',
          age: 30,
          email: '<EMAIL>',
        };

        const result = SchemaValidator.validate(schema, data);

        expect(result).toBe(true);
        expect(consoleErrorSpy).not.toHaveBeenCalled();
      });

      it('should fail if required field is missing even if types are correct', () => {
        const schema = {
          required: ['name', 'age'],
          properties: {
            name: { type: 'string' },
            age: { type: 'number' },
          },
        };
        const data = {
          name: 'John',
          // age is missing
        };

        const result = SchemaValidator.validate(schema, data);

        expect(result).toBe(false);
        expect(consoleErrorSpy).toHaveBeenCalledWith('Missing required field: age');
      });

      it('should fail if types are wrong even if all required fields are present', () => {
        const schema = {
          required: ['name', 'age'],
          properties: {
            name: { type: 'string' },
            age: { type: 'number' },
          },
        };
        const data = {
          name: 'John',
          age: '30', // wrong type
        };

        const result = SchemaValidator.validate(schema, data);

        expect(result).toBe(false);
        expect(consoleErrorSpy).toHaveBeenCalledWith(
          'Type mismatch for property "age": expected number, got string'
        );
      });
    });

    describe('edge cases', () => {
      it('should handle null data', () => {
        const schema = {
          required: ['name'],
          properties: {
            name: { type: 'string' },
          },
        };

        const result = SchemaValidator.validate(schema, null);

        expect(result).toBe(false);
        expect(consoleErrorSpy).toHaveBeenCalledWith('Missing required field: name');
      });

      it('should handle non-object data types', () => {
        const schema = {
          required: ['name'],
        };

        expect(SchemaValidator.validate(schema, 'string')).toBe(false);
        expect(SchemaValidator.validate(schema, 123)).toBe(false);
        expect(SchemaValidator.validate(schema, true)).toBe(false);
        expect(SchemaValidator.validate(schema, [])).toBe(false);
      });

      it('should handle empty schema', () => {
        const result = SchemaValidator.validate({}, { name: 'John' });

        expect(result).toBe(true);
        expect(consoleErrorSpy).not.toHaveBeenCalled();
      });

      it('should handle empty data with empty schema', () => {
        const result = SchemaValidator.validate({}, {});

        expect(result).toBe(true);
        expect(consoleErrorSpy).not.toHaveBeenCalled();
      });

      it('should handle required field that is not an array', () => {
        const schema = {
          required: 'name', // should be array but isn't
          properties: {
            name: { type: 'string' },
          },
        };
        const data = {
          name: 'John',
        };

        const result = SchemaValidator.validate(schema, data);

        expect(result).toBe(true); // doesn't break, just skips required validation
        expect(consoleErrorSpy).not.toHaveBeenCalled();
      });

      it('should handle properties that is not an object', () => {
        const schema = {
          properties: 'invalid', // should be object but isn't
        };
        const data = {
          name: 'John',
        };

        const result = SchemaValidator.validate(schema, data);

        expect(result).toBe(true); // doesn't break, just skips type validation
        expect(consoleErrorSpy).not.toHaveBeenCalled();
      });

      it('should handle complex nested data structures', () => {
        const schema = {
          required: ['user'],
          properties: {
            user: { type: 'object' },
            tags: { type: 'array' },
          },
        };
        const data = {
          user: {
            name: 'John',
            address: {
              street: '123 Main St',
              coordinates: [40.7128, -74.0060],
            },
          },
          tags: ['developer', 'javascript'],
        };

        const result = SchemaValidator.validate(schema, data);

        expect(result).toBe(true);
        expect(consoleErrorSpy).not.toHaveBeenCalled();
      });
    });
  });
}); 