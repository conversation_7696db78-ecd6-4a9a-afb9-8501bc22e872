/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import { isPrivateIp, fetchWithTimeout, FetchError } from './fetch.js';

// Mock global fetch
const mockFetch = vi.fn();
global.fetch = mockFetch;

// Mock timers
vi.useFakeTimers();

describe('fetch utilities', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.runOnlyPendingTimers();
    vi.useRealTimers();
    vi.useFakeTimers();
  });

  describe('FetchError', () => {
    it('should create FetchError with message and code', () => {
      const error = new FetchError('Network error', 'ETIMEDOUT');

      expect(error).toBeInstanceOf(Error);
      expect(error).toBeInstanceOf(FetchError);
      expect(error.message).toBe('Network error');
      expect(error.code).toBe('ETIMEDOUT');
      expect(error.name).toBe('FetchError');
    });

    it('should create FetchError with message only', () => {
      const error = new FetchError('Network error');

      expect(error).toBeInstanceOf(Error);
      expect(error).toBeInstanceOf(FetchError);
      expect(error.message).toBe('Network error');
      expect(error.code).toBeUndefined();
      expect(error.name).toBe('FetchError');
    });
  });

  describe('isPrivateIp', () => {
    it('should detect private IP ranges correctly', () => {
      // 10.x.x.x range
      expect(isPrivateIp('http://********')).toBe(true);
      expect(isPrivateIp('http://**************')).toBe(true);
      expect(isPrivateIp('https://********:8080/path')).toBe(true);

      // 127.x.x.x range (localhost)
      expect(isPrivateIp('http://127.0.0.1')).toBe(true);
      expect(isPrivateIp('http://***************')).toBe(true);

      // 172.16.x.x - 172.31.x.x range
      expect(isPrivateIp('http://**********')).toBe(true);
      expect(isPrivateIp('http://**************')).toBe(true);
      expect(isPrivateIp('http://**********')).toBe(true);

      // 192.168.x.x range
      expect(isPrivateIp('http://***********')).toBe(true);
      expect(isPrivateIp('http://***************')).toBe(true);
    });

    it('should detect IPv6 private addresses', () => {
      expect(isPrivateIp('http://[::1]')).toBe(true);
      expect(isPrivateIp('http://[fc00::1]')).toBe(true);
      expect(isPrivateIp('http://[fe80::1]')).toBe(true);
    });

    it('should return false for public IP addresses', () => {
      expect(isPrivateIp('http://*******')).toBe(false);
      expect(isPrivateIp('http://*******')).toBe(false);
      expect(isPrivateIp('http://**********')).toBe(false); // Just outside private range
      expect(isPrivateIp('http://**********')).toBe(false); // Just outside private range
      expect(isPrivateIp('http://********')).toBe(false); // Just outside 10.x range
    });

    it('should return false for domain names', () => {
      expect(isPrivateIp('http://google.com')).toBe(false);
      expect(isPrivateIp('https://api.example.com')).toBe(false);
      expect(isPrivateIp('http://localhost')).toBe(false); // hostname, not IP
    });

    it('should handle invalid URLs gracefully', () => {
      expect(isPrivateIp('not-a-url')).toBe(false);
      expect(isPrivateIp('')).toBe(false);
      expect(isPrivateIp('http://')).toBe(false);
      expect(isPrivateIp('invalid://url')).toBe(false);
    });

    it('should handle URLs with ports and paths', () => {
      expect(isPrivateIp('http://********:8080/api/test')).toBe(true);
      expect(isPrivateIp('https://***********:443/secure')).toBe(true);
      expect(isPrivateIp('http://*******:80/public')).toBe(false);
    });
  });

  describe('fetchWithTimeout', () => {
    beforeEach(() => {
      vi.useFakeTimers();
    });

    afterEach(() => {
      vi.useRealTimers();
    });

    it('should make successful fetch request', async () => {
      const mockResponse = new Response('test data', { status: 200 });
      mockFetch.mockResolvedValueOnce(mockResponse);

      const promise = fetchWithTimeout('http://example.com', 5000);
      
      const result = await promise;

      expect(mockFetch).toHaveBeenCalledWith('http://example.com', {
        signal: expect.any(AbortSignal),
      });
      expect(result).toBe(mockResponse);
    });

    it('should timeout after specified duration', async () => {
      // Mock fetch to never resolve
      const mockController = { abort: vi.fn() };
      vi.spyOn(global, 'AbortController').mockImplementation(() => mockController as any);
      
      mockFetch.mockImplementation(() => new Promise(() => {})); // Never resolves

      const promise = fetchWithTimeout('http://example.com', 1000);

      // Advance timers to trigger timeout
      vi.advanceTimersByTime(1000);

      await expect(promise).rejects.toThrow(FetchError);
      await expect(promise).rejects.toThrow('Request timed out after 1000ms');
      
      const error = await promise.catch(e => e);
      expect(error).toBeInstanceOf(FetchError);
      expect(error.code).toBe('ETIMEDOUT');
      expect(mockController.abort).toHaveBeenCalled();
    });

    it('should handle fetch errors and wrap them in FetchError', async () => {
      const fetchError = new Error('Network failure');
      mockFetch.mockRejectedValueOnce(fetchError);

      await expect(fetchWithTimeout('http://example.com', 5000)).rejects.toThrow(FetchError);
      await expect(fetchWithTimeout('http://example.com', 5000)).rejects.toThrow('Network failure');
    });

    it('should handle abort errors specifically', async () => {
      const abortError = new Error('Request aborted') as NodeJS.ErrnoException;
      abortError.code = 'ABORT_ERR';
      mockFetch.mockRejectedValueOnce(abortError);

      const promise = fetchWithTimeout('http://example.com', 1000);

      await expect(promise).rejects.toThrow(FetchError);
      const error = await promise.catch(e => e);
      expect(error.message).toBe('Request timed out after 1000ms');
      expect(error.code).toBe('ETIMEDOUT');
    });

    it('should clear timeout on successful response', async () => {
      const clearTimeoutSpy = vi.spyOn(global, 'clearTimeout');
      const mockResponse = new Response('success', { status: 200 });
      mockFetch.mockResolvedValueOnce(mockResponse);

      await fetchWithTimeout('http://example.com', 5000);

      expect(clearTimeoutSpy).toHaveBeenCalled();
    });

    it('should clear timeout on fetch error', async () => {
      const clearTimeoutSpy = vi.spyOn(global, 'clearTimeout');
      const fetchError = new Error('Network error');
      mockFetch.mockRejectedValueOnce(fetchError);

      try {
        await fetchWithTimeout('http://example.com', 5000);
      } catch {
        // Expected to throw
      }

      expect(clearTimeoutSpy).toHaveBeenCalled();
    });

    it('should handle different timeout values', async () => {
      const mockResponse = new Response('test', { status: 200 });
      mockFetch.mockResolvedValue(mockResponse);

      // Test with different timeout values
      await fetchWithTimeout('http://example.com', 100);
      await fetchWithTimeout('http://example.com', 10000);
      await fetchWithTimeout('http://example.com', 30000);

      expect(mockFetch).toHaveBeenCalledTimes(3);
    });

    it('should pass correct signal to fetch', async () => {
      const mockResponse = new Response('test', { status: 200 });
      mockFetch.mockResolvedValueOnce(mockResponse);

      await fetchWithTimeout('http://example.com', 5000);

      const fetchCall = mockFetch.mock.calls[0];
      expect(fetchCall[1]).toHaveProperty('signal');
      expect(fetchCall[1].signal).toBeInstanceOf(AbortSignal);
    });
  });
}); 