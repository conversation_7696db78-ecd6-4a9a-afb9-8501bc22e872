:root {
  --bg: #0b0d10;
  --surface: #111418;
  --overlay: #0f1216cc;
  --border: #222831;
  --text: #e6e9ef;
  --muted: #a9b1bd;
  --primary: #4f9cff;
  --primary-600: #3388ff;
  --success: #22c55e;
  --danger: #ef4444;
}

* { box-sizing: border-box; }
html, body { height: 100%; }
body {
  margin: 0;
  font-family: Inter, ui-sans-serif, system-ui, -apple-system, Segoe UI, Roboto, Arial, "Noto Sans", "Apple Color Emoji", "Segoe UI Emoji";
  color: var(--text);
  background: radial-gradient(1200px 600px at 10% -10%, #12223a 0, transparent 50%) no-repeat,
              radial-gradient(1200px 600px at 90% -10%, #1a163a 0, transparent 50%) no-repeat,
              var(--bg);
}

.container { max-width: 1080px; margin: 0 auto; padding: 0 16px; }
.muted { color: var(--muted); }
.price { color: var(--success); font-weight: 700; }

.site-header {
  position: sticky; top: 0; z-index: 20;
  background: linear-gradient(180deg, rgba(11,13,16,0.9), rgba(11,13,16,0.6));
  border-bottom: 1px solid var(--border);
  backdrop-filter: saturate(1.2) blur(8px);
}
.header-inner { display: flex; align-items: center; justify-content: space-between; height: 64px; }
.brand { display: flex; align-items: center; gap: 8px; font-weight: 700; }
.brand .logo { font-size: 20px; }
.brand .title { letter-spacing: 0.3px; }
.header-actions { display: flex; align-items: center; gap: 12px; }

.search {
  width: 240px; max-width: 40vw;
  height: 36px; padding: 6px 10px; border-radius: 8px;
  border: 1px solid var(--border); background: var(--surface); color: var(--text);
}
.cart-btn { height: 36px; padding: 6px 10px; border-radius: 8px; border: 1px solid var(--border); background: var(--surface); color: var(--text); cursor: pointer; }
.cart-btn:hover { border-color: var(--primary); }
.badge { margin-left: 6px; padding: 2px 8px; border-radius: 999px; background: var(--primary); color: white; font-weight: 700; }

.filters { display: flex; gap: 12px; align-items: center; padding: 16px 0 12px; }
.select { height: 36px; border-radius: 8px; border: 1px solid var(--border); background: var(--surface); color: var(--text); padding: 6px 10px; }

.product-grid {
  display: grid; grid-template-columns: repeat(auto-fill, minmax(220px, 1fr)); gap: 16px; padding: 16px 0 48px;
}
.card {
  border: 1px solid var(--border); background: #0d1116; border-radius: 12px; overflow: hidden;
  box-shadow: 0 0 0 1px #0d1116, 0 8px 28px rgba(0,0,0,0.4);
  display: flex; flex-direction: column;
}
.card img { width: 100%; height: 160px; object-fit: cover; background: #0a0c10; }
.card .content { padding: 12px; display: grid; gap: 6px; }
.card .title { font-weight: 600; }
.card .desc { color: var(--muted); font-size: 14px; min-height: 40px; }
.card .row { display: flex; align-items: center; justify-content: space-between; gap: 8px; }
.card .add-btn { cursor: pointer; border-radius: 8px; border: 1px solid var(--border); background: var(--surface); color: var(--text); padding: 8px 10px; }
.card .add-btn:hover { border-color: var(--primary); }

.cart-drawer {
  position: fixed; right: -420px; top: 0; width: 360px; max-width: 92vw; height: 100%; background: var(--surface);
  border-left: 1px solid var(--border); z-index: 40; transition: right .28s ease; display: flex; flex-direction: column;
}
.cart-drawer.open { right: 0; }
.mask { position: fixed; inset: 0; background: var(--overlay); opacity: 0; pointer-events: none; transition: opacity .28s ease; z-index: 30; }
.mask.show { opacity: 1; pointer-events: auto; }

.cart-header { display: flex; align-items: center; justify-content: space-between; padding: 16px; border-bottom: 1px solid var(--border); }
.icon-btn { background: transparent; border: 1px solid var(--border); color: var(--text); padding: 6px 8px; border-radius: 8px; cursor: pointer; }
.icon-btn:hover { border-color: var(--primary); }
.cart-items { flex: 1; overflow: auto; padding: 8px 12px; display: grid; gap: 8px; }
.cart-item { display: grid; grid-template-columns: 64px 1fr auto; gap: 8px; align-items: center; border: 1px solid var(--border); border-radius: 10px; padding: 8px; }
.cart-item img { width: 64px; height: 64px; object-fit: cover; border-radius: 8px; }
.qty { display: inline-flex; align-items: center; gap: 6px; }
.qty input { width: 48px; height: 28px; border-radius: 8px; border: 1px solid var(--border); background: var(--bg); color: var(--text); padding: 0 6px; }
.remove-btn { background: transparent; color: var(--danger); border: 1px solid var(--border); border-radius: 8px; padding: 6px 8px; cursor: pointer; }

.cart-footer { border-top: 1px solid var(--border); padding: 12px; display: grid; gap: 8px; }
.cart-total { display: flex; align-items: center; justify-content: space-between; }
.primary-btn { height: 40px; border-radius: 10px; border: none; background: linear-gradient(180deg, var(--primary), var(--primary-600)); color: white; font-weight: 700; cursor: pointer; }
.ghost-btn { height: 40px; border-radius: 10px; border: 1px solid var(--border); background: transparent; color: var(--text); cursor: pointer; }

.product-dialog { border: 1px solid var(--border); background: var(--surface); color: var(--text); border-radius: 12px; box-shadow: 0 12px 40px rgba(0,0,0,0.5); }
.product-dialog .dialog-body { display: grid; grid-template-columns: 1fr 1fr; gap: 16px; padding: 16px; }
.product-dialog img { width: 100%; height: 280px; object-fit: cover; border-radius: 10px; }
.product-dialog form { padding: 12px 16px; border-top: 1px solid var(--border); text-align: right; }
.price-row { display: flex; align-items: center; justify-content: space-between; gap: 12px; }

.checkout { background: linear-gradient(180deg, rgba(17,20,24,0.8), rgba(11,13,16,0.8)); border-top: 1px solid var(--border); border-bottom: 1px solid var(--border); }
.checkout.hidden { display: none; }
.checkout-inner { display: grid; grid-template-columns: 1fr 1fr; gap: 24px; padding: 24px 0; }
.form { display: grid; gap: 12px; }
.form label { display: grid; gap: 6px; font-size: 14px; color: var(--muted); }
.form input, .form textarea { border: 1px solid var(--border); background: var(--surface); color: var(--text); border-radius: 8px; padding: 8px 10px; }
.summary { border: 1px solid var(--border); background: #0d1116; border-radius: 12px; padding: 12px; display: grid; gap: 8px; }
.summary .row { display: flex; align-items: center; justify-content: space-between; }

.site-footer { padding: 24px 0; text-align: center; border-top: 1px solid var(--border); }

@media (max-width: 800px) {
  .product-dialog .dialog-body { grid-template-columns: 1fr; }
  .checkout-inner { grid-template-columns: 1fr; }
}


