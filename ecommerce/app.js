// 数据源（演示用）
const products = [
  { id: 'p-1', name: '经典白T恤', price: 59, category: '服饰', desc: '舒适纯棉，日常百搭。', image: 'https://images.unsplash.com/photo-1548883354-94bcfe321c0e?q=80&w=1200&auto=format&fit=crop' },
  { id: 'p-2', name: '黑色连帽卫衣', price: 199, category: '服饰', desc: '内里抓绒，保暖有型。', image: 'https://images.unsplash.com/photo-1544441893-675973e31985?q=80&w=1200&auto=format&fit=crop' },
  { id: 'p-3', name: '无线蓝牙耳机', price: 299, category: '数码', desc: '主动降噪，高清音质。', image: 'https://images.unsplash.com/photo-1518447437105-04db2e69ea95?q=80&w=1200&auto=format&fit=crop' },
  { id: 'p-4', name: '机械键盘', price: 399, category: '数码', desc: '青轴手感，RGB背光。', image: 'https://images.unsplash.com/photo-1517336714731-489689fd1ca8?q=80&w=1200&auto=format&fit=crop' },
  { id: 'p-5', name: '运动水壶', price: 69, category: '运动', desc: '轻便耐用，无异味。', image: 'https://images.unsplash.com/photo-1578874691223-64558a3caeca?q=80&w=1200&auto=format&fit=crop' },
  { id: 'p-6', name: '瑜伽垫', price: 129, category: '运动', desc: '加厚防滑，缓冲护膝。', image: 'https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?q=80&w=1200&auto=format&fit=crop' },
  { id: 'p-7', name: '法压壶', price: 139, category: '家居', desc: '随时享受手冲风味。', image: 'https://images.unsplash.com/photo-1595867818087-083862f3d8c8?q=80&w=1200&auto=format&fit=crop' },
  { id: 'p-8', name: '香薰蜡烛', price: 89, category: '家居', desc: '放松身心，营造氛围。', image: 'https://images.unsplash.com/photo-1488806374796-c963fbcf2e4c?q=80&w=1200&auto=format&fit=crop' },
];

// 工具函数
const formatCNY = (n) => `¥${n.toFixed(2)}`;

// 本地持久化购物车
class CartStore {
  constructor(storageKey = 'shoplite.cart') {
    this.storageKey = storageKey;
    this.items = this.#load();
  }
  #load() {
    try { return JSON.parse(localStorage.getItem(this.storageKey)) || []; } catch { return []; }
  }
  #save() { localStorage.setItem(this.storageKey, JSON.stringify(this.items)); }
  getTotalQuantity() { return this.items.reduce((s, it) => s + it.quantity, 0); }
  getTotalPrice() { return this.items.reduce((s, it) => s + it.quantity * it.price, 0); }
  upsert(product, delta = 1) {
    const idx = this.items.findIndex((it) => it.id === product.id);
    if (idx === -1) { this.items.push({ ...product, quantity: Math.max(1, delta) }); }
    else { this.items[idx].quantity = Math.max(1, this.items[idx].quantity + delta); }
    this.#save();
  }
  setQuantity(productId, quantity) {
    const idx = this.items.findIndex((it) => it.id === productId);
    if (idx !== -1) { this.items[idx].quantity = Math.max(1, quantity | 0); this.#save(); }
  }
  remove(productId) { this.items = this.items.filter((it) => it.id !== productId); this.#save(); }
  clear() { this.items = []; this.#save(); }
}

// 渲染器
const els = {
  grid: document.getElementById('productGrid'),
  cartCount: document.getElementById('cartCount'),
  cartDrawer: document.getElementById('cartDrawer'),
  drawerMask: document.getElementById('drawerMask'),
  cartItems: document.getElementById('cartItems'),
  cartTotal: document.getElementById('cartTotal'),
  productDialog: document.getElementById('productDialog'),
  dialogImage: document.getElementById('dialogImage'),
  dialogTitle: document.getElementById('dialogTitle'),
  dialogDesc: document.getElementById('dialogDesc'),
  dialogPrice: document.getElementById('dialogPrice'),
  dialogAddBtn: document.getElementById('dialogAddBtn'),
  searchInput: document.getElementById('searchInput'),
  categorySelect: document.getElementById('categorySelect'),
  sortSelect: document.getElementById('sortSelect'),
  checkoutSection: document.getElementById('checkoutSection'),
  checkoutForm: document.getElementById('checkoutForm'),
  checkoutSummary: document.getElementById('checkoutSummary'),
  year: document.getElementById('year'),
};

const cart = new CartStore();
let dialogProduct = null;

function renderCategories() {
  const cats = Array.from(new Set(products.map((p) => p.category)));
  for (const c of cats) {
    const opt = document.createElement('option');
    opt.value = c; opt.textContent = c; els.categorySelect.appendChild(opt);
  }
}

function applyFilters(list) {
  const keyword = (els.searchInput.value || '').trim().toLowerCase();
  const cat = els.categorySelect.value;
  const sort = els.sortSelect.value;
  let out = list.filter((p) => {
    const hitKw = p.name.toLowerCase().includes(keyword) || p.desc.toLowerCase().includes(keyword);
    const hitCat = cat === 'all' || p.category === cat;
    return hitKw && hitCat;
  });
  switch (sort) {
    case 'price-asc': out = out.sort((a,b)=>a.price-b.price); break;
    case 'price-desc': out = out.sort((a,b)=>b.price-a.price); break;
    case 'name-asc': out = out.sort((a,b)=>a.name.localeCompare(b.name)); break;
    case 'name-desc': out = out.sort((a,b)=>b.name.localeCompare(a.name)); break;
  }
  return out;
}

function renderProducts() {
  const filtered = applyFilters(products);
  els.grid.innerHTML = filtered.map((p) => `
    <article class="card" data-id="${p.id}">
      <img src="${p.image}" alt="${p.name}" />
      <div class="content">
        <div class="title">${p.name}</div>
        <div class="desc">${p.desc}</div>
        <div class="row">
          <span class="price">${formatCNY(p.price)}</span>
          <div>
            <button class="add-btn" data-action="quick-add">加入购物车</button>
            <button class="add-btn" data-action="view">详情</button>
          </div>
        </div>
      </div>
    </article>
  `).join('');
}

function renderCart() {
  els.cartItems.innerHTML = cart.items.map((it) => `
    <div class="cart-item" data-id="${it.id}">
      <img src="${it.image}" alt="${it.name}" />
      <div>
        <div style="font-weight:600">${it.name}</div>
        <div class="muted">${formatCNY(it.price)}</div>
        <div class="qty">
          <span>数量</span>
          <input type="number" min="1" step="1" value="${it.quantity}" data-action="qty" />
        </div>
      </div>
      <div style="display:grid; gap:8px; text-align:right">
        <strong>${formatCNY(it.price * it.quantity)}</strong>
        <button class="remove-btn" data-action="remove">移除</button>
      </div>
    </div>
  `).join('');
  els.cartTotal.textContent = formatCNY(cart.getTotalPrice());
  els.cartCount.textContent = String(cart.getTotalQuantity());
}

function openCart() {
  els.cartDrawer.classList.add('open');
  els.drawerMask.classList.add('show');
  els.cartDrawer.setAttribute('aria-hidden', 'false');
  els.drawerMask.setAttribute('aria-hidden', 'false');
  renderCart();
}
function closeCart() {
  els.cartDrawer.classList.remove('open');
  els.drawerMask.classList.remove('show');
  els.cartDrawer.setAttribute('aria-hidden', 'true');
  els.drawerMask.setAttribute('aria-hidden', 'true');
}

function openDialog(p) {
  dialogProduct = p;
  els.dialogImage.src = p.image;
  els.dialogTitle.textContent = p.name;
  els.dialogDesc.textContent = p.desc;
  els.dialogPrice.textContent = formatCNY(p.price);
  els.productDialog.showModal();
}

function closeDialog() { try { els.productDialog.close(); } catch {} }

function showCheckout() {
  closeCart();
  els.checkoutSection.classList.remove('hidden');
  renderCheckoutSummary();
  window.scrollTo({ top: document.body.scrollHeight, behavior: 'smooth' });
}
function hideCheckout() { els.checkoutSection.classList.add('hidden'); }

function renderCheckoutSummary() {
  if (cart.items.length === 0) {
    els.checkoutSummary.innerHTML = '<div class="muted">购物车为空</div>';
    return;
  }
  const rows = cart.items.map((it) => `
    <div class="row"><span>${it.name} × ${it.quantity}</span><span>${formatCNY(it.price * it.quantity)}</span></div>
  `).join('');
  els.checkoutSummary.innerHTML = `
    ${rows}
    <hr style="border:0;border-top:1px solid var(--border)" />
    <div class="row"><span>合计</span><strong>${formatCNY(cart.getTotalPrice())}</strong></div>
  `;
}

// 事件
document.getElementById('openCartBtn').addEventListener('click', openCart);
document.getElementById('closeCartBtn').addEventListener('click', closeCart);
els.drawerMask.addEventListener('click', closeCart);

els.grid.addEventListener('click', (e) => {
  const btn = e.target.closest('button');
  if (!btn) return;
  const card = btn.closest('.card');
  if (!card) return;
  const id = card.dataset.id;
  const p = products.find((x) => x.id === id);
  if (!p) return;
  const action = btn.dataset.action;
  if (action === 'quick-add') { cart.upsert(p, 1); renderCart(); }
  if (action === 'view') { openDialog(p); }
});

els.cartItems.addEventListener('input', (e) => {
  const input = e.target.closest('input[data-action="qty"]');
  if (!input) return;
  const id = input.closest('.cart-item').dataset.id;
  cart.setQuantity(id, Number(input.value));
  renderCart();
});

els.cartItems.addEventListener('click', (e) => {
  const btn = e.target.closest('button[data-action="remove"]');
  if (!btn) return;
  const id = btn.closest('.cart-item').dataset.id;
  cart.remove(id);
  renderCart();
});

document.getElementById('checkoutBtn').addEventListener('click', showCheckout);
document.getElementById('cancelCheckoutBtn').addEventListener('click', hideCheckout);

els.dialogAddBtn.addEventListener('click', () => {
  if (dialogProduct) { cart.upsert(dialogProduct, 1); renderCart(); }
  closeDialog();
});

els.productDialog.addEventListener('close', () => { dialogProduct = null; });

['input', 'change'].forEach((ev) => {
  els.searchInput.addEventListener(ev, renderProducts);
  els.categorySelect.addEventListener(ev, renderProducts);
  els.sortSelect.addEventListener(ev, renderProducts);
});

els.checkoutForm.addEventListener('submit', (e) => {
  e.preventDefault();
  if (cart.items.length === 0) { alert('购物车为空'); return; }
  const form = new FormData(els.checkoutForm);
  const order = {
    customer: Object.fromEntries(form.entries()),
    items: cart.items,
    total: cart.getTotalPrice(),
    createdAt: new Date().toISOString(),
  };
  console.log('订单示例(仅控制台):', order);
  alert('下单成功！(演示站点，不会实际下单)');
  cart.clear();
  renderCart();
  hideCheckout();
});

// 初始化
function init() {
  els.year.textContent = new Date().getFullYear();
  renderCategories();
  renderProducts();
  renderCart();
}
init();


