<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>简易电商网站</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="./styles.css" />
  </head>
  <body>
    <header class="site-header">
      <div class="container header-inner">
        <div class="brand">
          <span class="logo">🛍️</span>
          <span class="title">ShopLite</span>
        </div>
        <div class="header-actions">
          <input id="searchInput" class="search" placeholder="搜索商品..." />
          <button id="openCartBtn" class="cart-btn" aria-label="打开购物车">
            购物车 <span id="cartCount" class="badge">0</span>
          </button>
        </div>
      </div>
    </header>

    <main class="container">
      <section class="filters">
        <select id="categorySelect" class="select">
          <option value="all">全部分类</option>
        </select>
        <select id="sortSelect" class="select">
          <option value="default">默认排序</option>
          <option value="price-asc">价格：低到高</option>
          <option value="price-desc">价格：高到低</option>
          <option value="name-asc">名称：A→Z</option>
          <option value="name-desc">名称：Z→A</option>
        </select>
      </section>

      <section>
        <div id="productGrid" class="product-grid"></div>
      </section>
    </main>

    <aside id="cartDrawer" class="cart-drawer" aria-hidden="true">
      <div class="cart-header">
        <h2>我的购物车</h2>
        <button id="closeCartBtn" class="icon-btn" aria-label="关闭购物车">✕</button>
      </div>
      <div id="cartItems" class="cart-items"></div>
      <div class="cart-footer">
        <div class="cart-total">
          <span>合计：</span>
          <strong id="cartTotal">¥0.00</strong>
        </div>
        <button id="checkoutBtn" class="primary-btn">去结算</button>
      </div>
    </aside>
    <div id="drawerMask" class="mask" aria-hidden="true"></div>

    <dialog id="productDialog" class="product-dialog">
      <div class="dialog-body">
        <img id="dialogImage" alt="商品图片" />
        <div class="dialog-info">
          <h3 id="dialogTitle"></h3>
          <p id="dialogDesc" class="muted"></p>
          <div class="price-row">
            <span id="dialogPrice" class="price"></span>
            <button id="dialogAddBtn" class="primary-btn">加入购物车</button>
          </div>
        </div>
      </div>
      <form method="dialog">
        <button class="icon-btn">关闭</button>
      </form>
    </dialog>

    <section id="checkoutSection" class="checkout hidden">
      <div class="container checkout-inner">
        <div>
          <h2>结算信息</h2>
          <form id="checkoutForm" class="form">
            <label>姓名<input required name="name" /></label>
            <label>邮箱<input required type="email" name="email" /></label>
            <label>地址<textarea required name="address" rows="3"></textarea></label>
            <button class="primary-btn" type="submit">提交订单</button>
            <button class="ghost-btn" type="button" id="cancelCheckoutBtn">取消</button>
          </form>
        </div>
        <div>
          <h3>订单摘要</h3>
          <div id="checkoutSummary" class="summary"></div>
        </div>
      </div>
    </section>

    <footer class="site-footer">
      <div class="container">
        <span class="muted">© <span id="year"></span> ShopLite. 仅供演示。</span>
      </div>
    </footer>

    <script type="module" src="./app.js"></script>
  </body>
  </html>


