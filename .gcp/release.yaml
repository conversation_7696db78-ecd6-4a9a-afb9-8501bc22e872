steps:
  # Step 1: Install root dependencies (includes workspaces)
  - name: 'us-west1-docker.pkg.dev/gemini-code-dev/gemini-code-containers/gemini-code-builder'
    id: 'Install Dependencies'
    entrypoint: 'npm'
    args: ['install']

  # Step 2: Update version in root package.json
  - name: 'us-west1-docker.pkg.dev/gemini-code-dev/gemini-code-containers/gemini-code-builder'
    id: 'Set version in workspace root'
    entrypoint: 'bash'
    args:
      - -c # Use bash -c to allow for command substitution and string manipulation
      - |
        current_version=$(npm pkg get version | sed 's/"//g')
        if [ "$_OFFICIAL_RELEASE" = "true" ]; then
          new_version="$current_version"
        else
          new_version="${current_version}-rc.$_REVISION"
        fi
        npm pkg set "version=${new_version}"
        echo "Set root package.json version to: ${new_version}"

  # Step 3: Binds the package versions to the version in the repo root's package.json
  - name: 'us-west1-docker.pkg.dev/gemini-code-dev/gemini-code-containers/gemini-code-builder'
    id: 'Bind package versions to workspace root'
    entrypoint: 'npm'
    args: ['run', 'prerelease:dev'] # This will run prerelease:version and prerelease:deps

  # Step 4: Authenticate for Docker (so we can push images to the artifact registry)
  - name: 'us-west1-docker.pkg.dev/gemini-code-dev/gemini-code-containers/gemini-code-builder'
    id: 'Authenticate docker'
    entrypoint: 'npm'
    args: ['run', 'auth']

  # Step 5: Build workspace packages
  - name: 'us-west1-docker.pkg.dev/gemini-code-dev/gemini-code-containers/gemini-code-builder'
    id: 'Build packages'
    entrypoint: 'npm'
    args: ['run', 'build:packages']

  # Step 6: Prepare CLI package.json for publishing
  - name: 'us-west1-docker.pkg.dev/gemini-code-dev/gemini-code-containers/gemini-code-builder'
    id: 'Prepare @google/gemini-cli package.json and readme'
    entrypoint: 'npm'
    args: ['run', 'prepare:cli-packagejson']
    env:
      - 'GEMINI_SANDBOX=$_CONTAINER_TOOL'
      - 'SANDBOX_IMAGE_REGISTRY=$_SANDBOX_IMAGE_REGISTRY'
      - 'SANDBOX_IMAGE_NAME=$_SANDBOX_IMAGE_NAME'

  # Step 7: Build sandbox container image
  - name: 'us-west1-docker.pkg.dev/gemini-code-dev/gemini-code-containers/gemini-code-builder'
    id: 'Build sandbox Docker image'
    entrypoint: 'npm'
    args: ['run', 'build:sandbox:fast']
    env:
      - 'GEMINI_SANDBOX=$_CONTAINER_TOOL'
      - 'SANDBOX_IMAGE_REGISTRY=$_SANDBOX_IMAGE_REGISTRY'
      - 'SANDBOX_IMAGE_NAME=$_SANDBOX_IMAGE_NAME'

  # Step 8: Publish sandbox container image
  - name: 'us-west1-docker.pkg.dev/gemini-code-dev/gemini-code-containers/gemini-code-builder'
    id: 'Publish sandbox Docker image'
    entrypoint: 'npm'
    args: ['run', 'publish:sandbox']
    env:
      - 'GEMINI_SANDBOX=$_CONTAINER_TOOL'
      - 'SANDBOX_IMAGE_REGISTRY=$_SANDBOX_IMAGE_REGISTRY'
      - 'SANDBOX_IMAGE_NAME=$_SANDBOX_IMAGE_NAME'

  # Pre-Step 9: authenticate to our intermediate npm registry
  - name: 'us-west1-docker.pkg.dev/gemini-code-dev/gemini-code-containers/gemini-code-builder'
    id: 'Setup @google/gemini-cli-core auth token for publishing'
    entrypoint: 'bash'
    args:
      - -c
      - |
        echo "//wombat-dressing-room.appspot.com/:_authToken=$$CORE_PACKAGE_PUBLISH_TOKEN" > $$HOME/.npmrc
    secretEnv: ['CORE_PACKAGE_PUBLISH_TOKEN']

  # Step 9: Publish @google/gemini-cli-core to NPM
  - name: 'us-west1-docker.pkg.dev/gemini-code-dev/gemini-code-containers/gemini-code-builder'
    id: 'Publish @google/gemini-cli-core package'
    entrypoint: 'bash'
    args:
      - -c
      - |
        if [ "$_OFFICIAL_RELEASE" = "true" ]; then
          npm publish --workspace=@google/gemini-cli-core --tag=latest
        else
          npm publish --workspace=@google/gemini-cli-core --tag=rc
        fi
    env:
      - 'GEMINI_SANDBOX=$_CONTAINER_TOOL'
      - 'SANDBOX_IMAGE_REGISTRY=$_SANDBOX_IMAGE_REGISTRY'
      - 'SANDBOX_IMAGE_NAME=$_SANDBOX_IMAGE_NAME'

  # Pre-Step 10: authenticate to our intermediate npm registry
  - name: 'us-west1-docker.pkg.dev/gemini-code-dev/gemini-code-containers/gemini-code-builder'
    id: 'Setup @google/gemini-cli auth token for publishing'
    entrypoint: 'bash'
    args:
      - -c
      - |
        echo "//wombat-dressing-room.appspot.com/:_authToken=$$CLI_PACKAGE_PUBLISH_TOKEN" > $$HOME/.npmrc
    secretEnv: ['CLI_PACKAGE_PUBLISH_TOKEN']

  # Step 10: Publish @google/gemini-cli to NPM
  - name: 'us-west1-docker.pkg.dev/gemini-code-dev/gemini-code-containers/gemini-code-builder'
    id: 'Publish @google/gemini-cli package'
    entrypoint: 'bash'
    args:
      - -c
      - |
        if [ "$_OFFICIAL_RELEASE" = "true" ]; then
          npm publish --workspace=@google/gemini-cli --tag=latest
        else
          npm publish --workspace=@google/gemini-cli --tag=rc
        fi
    env:
      - 'GEMINI_SANDBOX=$_CONTAINER_TOOL'
      - 'SANDBOX_IMAGE_REGISTRY=$_SANDBOX_IMAGE_REGISTRY'
      - 'SANDBOX_IMAGE_NAME=$_SANDBOX_IMAGE_NAME'

options:
  defaultLogsBucketBehavior: REGIONAL_USER_OWNED_BUCKET
  dynamicSubstitutions: true

availableSecrets:
  secretManager:
    - versionName: ${_CLI_PACKAGE_WOMBAT_TOKEN_RESOURCE_NAME}
      env: 'CLI_PACKAGE_PUBLISH_TOKEN'
    - versionName: ${_CORE_PACKAGE_WOMBAT_TOKEN_RESOURCE_NAME}
      env: 'CORE_PACKAGE_PUBLISH_TOKEN'

substitutions:
  _REVISION: '0'
  _OFFICIAL_RELEASE: 'false'
  _CONTAINER_TOOL: 'docker'
  _SANDBOX_IMAGE_REGISTRY: ''
  _SANDBOX_IMAGE_NAME: ''
  _CLI_PACKAGE_WOMBAT_TOKEN_RESOURCE_NAME: ''
  _CORE_PACKAGE_WOMBAT_TOKEN_RESOURCE_NAME: ''
