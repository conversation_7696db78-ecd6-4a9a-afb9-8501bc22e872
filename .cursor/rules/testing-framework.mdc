---
description: 
globs: 
alwaysApply: false
---
# 单元测试规范：使用现有测试框架

## 核心原则
**写单测时永远尝试使用现有的测试框架，不要引入新的测试工具或框架。**

## 项目测试框架
本项目使用 **Vitest** 作为统一的测试框架，配置文件位于：
- [packages/core/vitest.config.ts](mdc:packages/core/vitest.config.ts)
- [packages/cli/vitest.config.ts](mdc:packages/cli/vitest.config.ts)

## 标准测试模式
参考现有测试文件的标准模式：

### 1. 导入方式
```typescript
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
```

### 2. 测试结构参考
以 [packages/core/src/core/prompts.test.ts](mdc:packages/core/src/core/prompts.test.ts) 为例：
- 使用 `describe` 组织测试套件
- 使用 `it` 编写测试用例  
- 使用 `vi.mock()` 进行模块Mock
- 使用 `expect().toMatchSnapshot()` 进行快照测试

### 3. Mock策略参考
以 [packages/core/src/tools/tool-registry.test.ts](mdc:packages/core/src/tools/tool-registry.test.ts) 为例：
- 使用 `vi.hoisted()` 提升Mock函数
- 使用 `vi.spyOn()` 监听函数调用
- 在 `beforeEach/afterEach` 中管理Mock状态

## 测试命令
- 运行测试：`npm run test` 
- 带覆盖率：`npm run test:ci`
- 配置支持JUnit报告和多种覆盖率格式

## 禁止操作
❌ 不要引入Jest、Mocha、Jasmine等其他测试框架  
❌ 不要修改vitest配置除非有明确需求  
❌ 不要跳过现有的测试约定和模式

## 最佳实践
✅ 遵循现有测试文件的结构和命名约定  
✅ 使用项目已有的Mock策略  
✅ 充分利用vitest的快照测试功能  
✅ 保持测试的可读性和可维护性
